body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1050px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f8fa;
}

.container {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    color: #1da1f2;
    text-align: center;
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e1e8ed;
    border-radius: 5px;
    font-size: 16px;
}

button {
    background-color: #1da1f2;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #1991da;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.loading {
    display: none;
    text-align: center;
    margin: 20px 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1da1f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result {
    display: none;
    margin-top: 20px;
    padding: 20px;
    border-radius: 5px;
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.result pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.tweet-info {
    margin-bottom: 15px;
    line-height: 1.6;
}

.tweet-info p {
    margin: 8px 0;
}

.label {
    color: #666;
    font-weight: 500;
    margin-right: 8px;
}

.value {
    color: #1a1a1a;
    font-weight: 600;
}

.tweet-stats {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    padding: 15px;
    background-color: #e8f5fe;
    border-radius: 5px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    flex: 1;
    justify-content: center;
}

.stat-item:not(:last-child) {
    border-right: 1px solid #cce5ff;
}

.stat-item .label {
    white-space: nowrap;
}

.stat-item .value {
    min-width: 40px;
    text-align: right;
}

.prediction-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px dashed #1da1f2;
}

.prediction-section h3 {
    color: #1da1f2;
    margin-bottom: 15px;
}

.prediction-item {
    margin: 10px 0;
    padding: 8px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px 35px 10px 10px;
    border-radius: 5px;
    margin-top: 10px;
    display: none;
    position: relative;
}

.error-message {
    display: block;
    line-height: 1.5;
}

.error-close {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 0;
    font-size: 20px;
    line-height: 1;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.error-close:hover {
    color: #bd2130;
}

.emo-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px dashed #1da1f2;
}

.emo-section h3 {
    color: #1da1f2;
    margin-bottom: 15px;
}

.tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    background-color: #e1e8ed;
}

.tab-btn.active {
    background-color: #1da1f2;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* History styles */
.history-list {
    margin-top: 20px;
}

.clear-all-btn {
    margin-bottom: 20px;
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.clear-all-btn:hover {
    background-color: #c82333;
}

.history-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.history-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.history-item .meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9em;
}

.history-item .content {
    margin: 10px 0;
}

.history-item .content p {
    margin: 0;
    color: #1a1a1a;
}

.history-item .stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 10px;
    color: #666;
}

.delete-btn {
    background-color: transparent;
    color: #dc3545;
    border: 1px solid #dc3545;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8em;
}

.delete-btn:hover {
    background-color: #dc3545;
    color: white;
}

.empty-history {
    text-align: center;
    padding: 40px;
    color: #666;
}

.error-message {
    text-align: center;
    padding: 20px;
    color: #dc3545;
    background-color: #f8d7da;
    border-radius: 4px;
}

/* History Table Styles */
.history-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.history-table th,
.history-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
    font-size: 14px;
}

.history-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
}

.history-table tbody tr:hover {
    background-color: #f5f5f5;
}

.history-table td {
    color: #666;
}

/* Make the history tab content scrollable */
#history-tab {
    max-height: 80vh;
    overflow-y: auto;
}

/* Empty history message */
.empty-history {
    text-align: center;
    padding: 40px;
    color: #666;
}

.empty-history p {
    margin: 0;
    font-size: 16px;
}

.toggle-container {
    position: relative;
    display: inline-block;
    margin: 0 10px;
    cursor: pointer;
}

.toggle-container input[type="checkbox"] {
    display: none;
}

.toggle-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.toggle-button:hover {
    background-color: #e0e0e0;
}

.toggle-container input[type="checkbox"]:checked + .toggle-button {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
} 