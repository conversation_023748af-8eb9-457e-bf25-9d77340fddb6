const predictBtn = document.getElementById('predictBtn');
const tweetIdInput = document.getElementById('tweetId');
const loading = document.getElementById('loading');
const result = document.getElementById('result');
const error = document.getElementById('error');
const historyList = document.getElementById('historyList');
const tabBtns = document.querySelectorAll('.tab-btn');
const tabContents = document.querySelectorAll('.tab-content');

// Initialize prediction history
let predictionHistory = JSON.parse(localStorage.getItem('predictionHistory') || '[]');

// Hide all tab contents initially except the first one
document.querySelectorAll('.tab-content').forEach((content, index) => {
    content.style.display = index === 0 ? 'block' : 'none';
});

// Tab functionality
tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
        const tabId = btn.dataset.tab;
        switchTab(tabId);
    });
});

function switchTab(tabId) {
    // Update button states
    tabBtns.forEach(btn => {
        btn.classList.toggle('active', btn.dataset.tab === tabId);
    });

    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });

    // Show the selected tab content
    const activeTab = document.getElementById(`${tabId}-tab`);
    if (activeTab) {
        activeTab.style.display = 'block';
    }

    if (tabId === 'history') {
        loadPredictionHistory();
    }
}

function savePredictionToHistory(tweetId, data) {
    const prediction = {
        id: tweetId,
        timestamp: new Date().toISOString(),
        data: data
    };
    
    predictionHistory.unshift(prediction);
    if (predictionHistory.length > 50) {
        predictionHistory.pop();
    }
    
    localStorage.setItem('predictionHistory', JSON.stringify(predictionHistory));
    
    if (document.getElementById('history-tab').classList.contains('active')) {
        loadPredictionHistory();
    }
}

async function loadPredictionHistory() {
    try {
        const response = await fetch('http://localhost:8080/api/history');
        const data = await response.json();

        if (data.status !== 'success') {
            historyList.innerHTML = `
                <div class="empty-history">
                    <p>加载历史记录失败</p>
                </div>
            `;
            return;
        }

        if (!data.data || data.data.length === 0) {
            historyList.innerHTML = `
                <div class="empty-history">
                    <p>暂无预测历史记录</p>
                </div>
            `;
            return;
        }

        const tableHTML = `
            <table class="history-table">
                <thead>
                    <tr>
                        <th>推文ID</th>
                        <th>发布时间</th>
                        <th>预测时间</th>
                        <th>情感类型</th>
                        <th>一小时转发</th>
                        <th>预测转发</th>
                        <th>实际转发</th>
                        <th>误差</th>
                        <th>一小时评论</th>
                        <th>预测评论</th>
                        <th>实际评论</th>
                        <th>误差</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.data.map(item => `
                        <tr>
                            <td>${
                                item.source === 0 ? 
                                `<a href="https://weibo.com/1/${item.tweet_id}" target="_blank">${item.tweet_id}</a>` :
                                item.source === 1 ?
                                `<a href="https://x.com/i/web/status/${item.tweet_id}" target="_blank">${item.tweet_id}</a>` :
                                item.tweet_id
                            }</td>
                            <td>${new Date(item.created_at).toLocaleString('zh-CN')}</td>
                            <td>${new Date(item.crawl_time).toLocaleString('zh-CN')}</td>
                            <td>${item.emo_text}</td>
                            <td>${item.repost_one_hour_number < 0 ? '-' : item.repost_one_hour_number}</td>
                            <td>${item.repost_pred_num < 0 ? '-' : item.repost_pred_num}</td>
                            <td>${item.repost_true_num < 0 ? '-' : item.repost_true_num}</td>
                            <td>${item.repost_true_num < 0 ? '-' : ((item.repost_pred_num - item.repost_true_num) > 0 ? '+' : '') + (item.repost_pred_num - item.repost_true_num)}</td>
                            <td>${item.comment_one_hour_number}</td>
                            <td>${item.comment_pred_num}</td>
                            <td>${item.comment_true_num < 0 ? '-' : item.comment_true_num}</td>
                            <td>${item.comment_true_num < 0 ? '-' : ((item.comment_pred_num - item.comment_true_num) > 0 ? '+' : '') + (item.comment_pred_num - item.comment_true_num)}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        historyList.innerHTML = tableHTML;
    } catch (error) {
        historyList.innerHTML = `
            <div class="empty-history">
                <p>加载历史记录失败: ${error.message}</p>
            </div>
        `;
    }
}

document.getElementById('predictBtn').addEventListener('click', async () => {
    const tweetId = document.getElementById('tweetId').value;
    const refreshToggle = document.getElementById('refreshToggle').checked;
    
    if (!tweetId) {
        showError('请输入推文ID');
        return;
    }

    showLoading();
    try {
        const response = await fetch(`http://localhost:8080/api/predict?tweet_id=${tweetId}&refresh=${refreshToggle}`);
        const data = await response.json();

        if (data.status === 'error') {
            showError(data.error);
        } else {
            showResult(data.data);
        }
    } catch (err) {
        console.log(`Error ${err}`);
        showError('无法连接到服务器，请稍后重试。');
    } finally {
        hideLoading();
    }
});

function showLoading() {
    loading.style.display = 'block';
    predictBtn.disabled = true;
}

function hideLoading() {
    loading.style.display = 'none';
    predictBtn.disabled = false;
}

function showResult(data) {
    result.style.display = 'block';
    const formattedResult = `
        <div class="tweet-info">
            <p><span class="label">👤 作者：</span><span class="value">${data.author}</span></p>
            <p><span class="label">🕒 发布时间：</span><span class="value">${data.created_at}</span></p>
            <p><span class="label">📝 内容：</span><span class="value">${data.tweet_content}</span></p>
        </div>
        <div class="tweet-stats">
            <div class="stat-item">
                <span class="label">🔄 转发数：</span>
                <span class="value">${data.reposts_count}</span>
            </div>
            <div class="stat-item">
                <span class="label">💬 评论数：</span>
                <span class="value">${data.comments_count}</span>
            </div>
            <div class="stat-item">
                <span class="label">❤️ 点赞数：</span>
                <span class="value">${data.attitudes_count}</span>
            </div>
        </div>
        <div class="emo-section">
            <h3>细粒度情感分析</h3>
            <div class="prediction-item">
                <span class="label">情感分类：</span>
                <span class="value">${data.emo_label}</span>
                <span class="label" style="margin-left: 20px;">情感得分：</span>
                <span class="value">${data.emo_score}</span>
            </div>
        </div>
        <div class="prediction-section">
            <h3>预测结果</h3>
            <div class="prediction-item">
                <span class="label">一小时内转发数：</span>
                <span class="value">${data.repost_observed_count}</span>
            </div>
            <div class="prediction-item">
                <span class="label">预测一天后的转发数：</span>
                <span class="value">${data.repost_total_prediction}</span>
            </div>

            <div class="prediction-item">
                <span class="label">一小时内评论数：</span>
                <span class="value">${data.comment_observed_count}</span>
            </div>
            <div class="prediction-item">
                <span class="label">预测一天后的评论数：</span>
                <span class="value">${data.comment_total_prediction}</span>
            </div>
        </div>
    `;
    result.innerHTML = formattedResult;
}

function hideResult() {
    result.style.display = 'none';
}

function showError(message) {
    error.querySelector('.error-message').textContent = message;
    error.style.display = 'block';
    hideResult();
}

function hideError() {
    error.style.display = 'none';
}

// Initialize the active tab
document.querySelector('.tab-btn.active').click(); 