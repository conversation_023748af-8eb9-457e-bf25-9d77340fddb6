import os
from subprocess import run, CalledProcessError, PIPE
import json
import datetime
import mysql.connector
import time
import traceback
import threading

def cron_once():
    # 一次性的定时任务

    user_ids = ['lidangzzz']
    tasks = crawl_by_user_ids(user_ids)
    save2db(tasks)
    print(f'Cron job completed. {len(tasks)} tasks added to database.')



def cron_scan(start_now=False):
    # 定时任务
    # 每小时运行一次 cron_once()，当调用线程退出时自动退出

    def background_task():
        if not start_now:
            # Sleep for 1 hour (3600 seconds)
            time.sleep(3600)
        while True:
            try:
                print(f"[{datetime.now()}] Starting cron [SCAN_TWITTER] job...")
                cron_once()
                print(f"[{datetime.now()}] Cron job [SCAN_TWITTER] completed")
            except Exception as e:
                print(f"[{datetime.now()}] Error in cron job: {e}")
                traceback.print_exc()

            # Sleep for 1 day
            time.sleep(3600*24)

    # Create and start daemon thread
    # Daemon threads automatically exit when the main program exits
    cron_thread = threading.Thread(target=background_task, daemon=True)
    cron_thread.start()

    print("Cron background [SCAN_TWITTER] task started (runs every 1 day)")
    return cron_thread

    

def crawl_by_user_ids(user_ids):
    from twitterSelenium.crawl_by_user_id import crawl_multiple_users

    start_time = datetime.datetime.now() - datetime.timedelta(days=1)

    tweets = crawl_multiple_users(user_ids, start_time=start_time)
    
    tasks = []
    for user_id, user_tweets in tweets.items():
        for tweet in user_tweets:
            tasks.append({
                'tweet_id': tweet['tweet_id'],
                'created_at': int(tweet['created_at'].timestamp())
            })
    
    return tasks



def save2db(tasks):
    # MySQL Configuration (same as in cron_crawl.py)
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '123456',
        'database': 'cas',
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }

    if not tasks:
        print("No tasks to save to database")
        return

    try:
        # Connect to database
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # Insert tasks into tweet_task table
        # id is auto_increment, so we don't include it in the INSERT
        insert_query = """
        INSERT INTO tweet_task (tweet_id, created_at, has_crawled, source)
        VALUES (%s, FROM_UNIXTIME(%s), %s, %s)
        """

        # Prepare data for batch insert
        insert_data = []
        for task in tasks:
            tweet_id = task['tweet_id']
            created_at = task['created_at']  # This is already a unix timestamp
            has_crawled = 0
            source = 1
            insert_data.append((tweet_id, created_at, has_crawled, source))

        # Execute batch insert
        cursor.executemany(insert_query, insert_data)
        conn.commit()

        print(f"Successfully saved {len(tasks)} tasks to database")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error saving tasks to database: {e}")
        if 'conn' in locals():
            conn.rollback()
            cursor.close()
            conn.close()

    

if __name__ == '__main__':
    cron_once()