#!/usr/bin/env python
# encoding: utf-8
"""
Author: nghuyong
Mail: <EMAIL>
Created Time: 2020/4/14
"""
import datetime
import json
import re

from scrapy import Spider
from scrapy.http import Request
from spiders.common import parse_tweet_info, parse_long_tweet


class TweetSpiderByUserID(Spider):
    """
    用户推文数据采集
    """
    name = "tweet_spider_by_user_id"

    def __init__(self, tweet_user_ids=None, *args, **kwargs):
        """
        Initialize spider with optional tweet_id
        Args:
            tweet_id: The ID of the tweet to crawl
        """
        super(TweetSpiderByUserID, self).__init__(*args, **kwargs)
        if not tweet_user_ids:
            raise ValueError("tweet_user_ids parameter is required")
        self.tweet_user_ids = tweet_user_ids

    def start_requests(self):
        """
        爬虫入口
        """
        end_time = datetime.datetime.now()
        start_time = datetime.datetime.now() - datetime.timedelta(days=1)
        
        for user_id in self.tweet_user_ids:
            url = f"https://weibo.com/ajax/statuses/searchProfile?uid={user_id}&page=1&hasori=1&hastext=1&haspic=1&hasvideo=1&hasmusic=1&hasret=1"
            tmp_url = url + f'&starttime={int(start_time.timestamp())}&endtime={int(end_time.timestamp())}'
            yield Request(tmp_url, callback=self.parse, meta={'user_id': user_id, 'page_num': 1})

        # 这里user_ids可替换成实际待采集的数据
        # user_ids = ['1087770692']
        # 这里的时间替换成实际需要的时间段，如果要采集用户全部推文 is_crawl_specific_time_span 设置为False
        # is_crawl_specific_time_span = True
        # start_time = datetime.datetime.now() - datetime.timedelta(hours=1)
        # end_time = datetime.datetime.now()
        # for user_id in self.tweet_user_ids:
        #     url = f"https://weibo.com/ajax/statuses/searchProfile?uid={user_id}&page=1&hasori=1&hastext=1&haspic=1&hasvideo=1&hasmusic=1&hasret=1"
        #     if not is_crawl_specific_time_span:
        #         yield Request(url, callback=self.parse, meta={'user_id': user_id, 'page_num': 1})
        #     else:
        #         # 切分成10天进行
        #         tmp_start_time = start_time
        #         while tmp_start_time <= end_time:
        #             tmp_end_time = tmp_start_time + datetime.timedelta(days=10)
        #             tmp_end_time = min(tmp_end_time, end_time)
        #             tmp_url = url + f'&starttime={int(tmp_start_time.timestamp())}&endtime={int(tmp_end_time.timestamp())}'
        #             yield Request(tmp_url, callback=self.parse, meta={'user_id': user_id, 'page_num': 1})
        #             tmp_start_time = tmp_end_time + datetime.timedelta(days=1)

    def parse(self, response, **kwargs):
        """
        网页解析
        """
        data = json.loads(response.text)
        tweets = data['data']['list']
        for tweet in tweets:
            item = parse_tweet_info(tweet)
            del item['user']
            if item['isLongText']:
                url = "https://weibo.com/ajax/statuses/longtext?id=" + item['mblogid']
                yield Request(url, callback=parse_long_tweet, meta={'item': item})
            else:
                yield item
        if tweets:
            user_id, page_num = response.meta['user_id'], response.meta['page_num']
            url = response.url.replace(f'page={page_num}', f'page={page_num + 1}')
            yield Request(url, callback=self.parse, meta={'user_id': user_id, 'page_num': page_num + 1})
