#!/usr/bin/env python
# encoding: utf-8
"""
Author: nghuyong
Mail: <EMAIL>
Created Time: 2020/4/14
"""
import json
from scrapy import <PERSON>
from scrapy.http import Request
from spiders.common import parse_tweet_info, parse_long_tweet


class TweetSpiderByTweetID(Spider):
    """
    用户推文ID采集推文
    """
    name = "tweet_by_tweet_id"
    base_url = "https://weibo.cn"

    def __init__(self, tweet_id=None, *args, **kwargs):
        """
        Initialize spider with optional tweet_id
        Args:
            tweet_id: The ID of the tweet to crawl
        """
        super(TweetSpiderByTweetID, self).__init__(*args, **kwargs)
        if not tweet_id:
            raise ValueError("tweet_id parameter is required")
        self.tweet_id = tweet_id

    @staticmethod
    def get_tweet_url(tweet_id):
        """
        Generate tweet URL for a given tweet ID
        """
        return f"https://weibo.com/ajax/statuses/show?id={tweet_id}"

    def start_requests(self):
        """
        爬虫入口
        """
        url = self.get_tweet_url(self.tweet_id)
        yield Request(url, callback=self.parse)

    def parse(self, response, **kwargs):
        """
        网页解析
        """
        data = json.loads(response.text)
        item = parse_tweet_info(data)
        if item.get('isLongText'):
            url = "https://weibo.com/ajax/statuses/longtext?id=" + item['mblogid']
            yield Request(url, callback=parse_long_tweet, meta={'item': item})
        yield item 