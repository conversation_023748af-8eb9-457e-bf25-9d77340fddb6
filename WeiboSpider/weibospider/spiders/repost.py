#!/usr/bin/env python
# encoding: utf-8
"""
Author: nghuyong
Mail: <EMAIL>
Created Time: 2020/4/14
"""
import json
from scrapy import <PERSON>
from scrapy.http import Request
from spiders.common import parse_tweet_info, url_to_mid


class RepostSpider(Spider):
    """
    微博转发数据采集
    """
    name = "repost"
    
    def __init__(self, tweet_id=None, *args, **kwargs):
        """
        Initialize spider with optional tweet_id
        Args:
            tweet_id: The ID of the tweet to crawl reposts for
        """
        super(RepostSpider, self).__init__(*args, **kwargs)
        if not tweet_id:
            raise ValueError("tweet_id parameter is required")
        self.tweet_id = tweet_id

    @staticmethod
    def get_repost_url(mid, page=1):
        """
        Generate repost URL for a given tweet ID and page
        """
        return f"https://weibo.com/ajax/statuses/repostTimeline?id={mid}&page={page}&moduleID=feed&count=10"

    def start_requests(self):
        """
        爬虫入口
        """
        mid = url_to_mid(self.tweet_id)
        url = self.get_repost_url(mid)
        yield Request(url, callback=self.parse, meta={'page_num': 1, 'mid': mid})

    def parse(self, response, **kwargs):
        """
        网页解析
        """
        data = json.loads(response.text)
        for tweet in data['data']:
            item = parse_tweet_info(tweet)
            yield item
        if data['data']:
            mid, page_num = response.meta['mid'], response.meta['page_num']
            page_num += 1
            url = self.get_repost_url(mid, page_num)
            yield Request(url, callback=self.parse, meta={'page_num': page_num, 'mid': mid})
