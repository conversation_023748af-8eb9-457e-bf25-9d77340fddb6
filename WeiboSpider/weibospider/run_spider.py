#!/usr/bin/env python
# encoding: utf-8
"""
Author: nghuyong
Mail: <EMAIL>
Created Time: 2019-12-07 21:27
"""
import argparse
import os
from scrapy.crawler import CrawlerProcess
from scrapy.utils.project import get_project_settings
from spiders.tweet_by_user_id import TweetSpiderByUserID
from spiders.tweet_by_keyword import TweetSpiderByKeyword
from spiders.tweet_by_tweet_id import TweetSpiderBy<PERSON>weet<PERSON>
from spiders.comment import CommentSpider
from spiders.follower import FollowerSpider
from spiders.user import User<PERSON>pider
from spiders.fan import FanSpider
from spiders.repost import Repost<PERSON>pider

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Run Weibo Spider')
    parser.add_argument('mode', choices=[
        'comment', 'fan', 'follow', 'user', 'repost',
        'tweet_by_tweet_id', 'tweet_by_user_id', 'tweet_by_keyword'
    ], help='Spider mode to run')

    # Single value arguments
    parser.add_argument('--tweet_id', type=str, help='Tweet ID')
    parser.add_argument('--user_id', type=str, help='User ID')
    parser.add_argument('--keyword', type=str, help='Keyword for search')

    # List arguments
    parser.add_argument('--tweet_user_ids', nargs='+', help='List of user IDs')
    parser.add_argument('--keywords', nargs='+', help='List of keywords')
    parser.add_argument('--tweet_ids', nargs='+', help='List of tweet IDs')

    return parser.parse_args()

if __name__ == '__main__':
    args = parse_arguments()

    # Build kwargs from parsed arguments
    kwargs = {}
    for arg_name, arg_value in vars(args).items():
        if arg_name != 'mode' and arg_value is not None:
            kwargs[arg_name] = arg_value

    os.environ['SCRAPY_SETTINGS_MODULE'] = 'settings'
    settings = get_project_settings()
    process = CrawlerProcess(settings)
    mode_to_spider = {
        'comment': CommentSpider,
        'fan': FanSpider,
        'follow': FollowerSpider,
        'user': UserSpider,
        'repost': RepostSpider,
        'tweet_by_tweet_id': TweetSpiderByTweetID,
        'tweet_by_user_id': TweetSpiderByUserID,
        'tweet_by_keyword': TweetSpiderByKeyword,
    }
    spider_cls = mode_to_spider[args.mode]
    process.crawl(spider_cls, **kwargs)
    # the script will block here until the crawling is finished
    process.start()
