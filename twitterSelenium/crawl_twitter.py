from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os
import json
from datetime import datetime
from zoneinfo import ZoneInfo
import re

class TwitterCrawler:
    # Static variables for browser instance
    _driver = None
    _wait = None
    
    @classmethod
    def init_browser(cls):
        """Initialize browser if not already initialized"""
        if cls._driver is None:
            options = Options()
            options.add_argument(f"--user-data-dir={USER_DATA_DIR}")
            options.add_argument(f"--profile-directory={PROFILE_DIRECTORY}")
            options.add_argument("--disable-gpu")
            
            service = Service(executable_path=CHROMEDRIVER_PATH)
            cls._driver = webdriver.Chrome(service=service, options=options)
            cls._wait = WebDriverWait(cls._driver, 30)
            cls._driver.maximize_window()
            print("Chrome opened with your existing profile.")
    
    @classmethod
    def close_browser(cls):
        """Close the browser instance if it exists"""
        if cls._driver is not None:
            cls._driver.quit()
            cls._driver = None
            cls._wait = None
            print("Browser closed.")
    
    @classmethod
    def get_browser(cls):
        """Get or create browser instance"""
        if cls._driver is None:
            cls.init_browser()
        return cls._driver, cls._wait

# --- Configuration ---
# Replace with the path to your Chrome User Data directory
USER_DATA_DIR = os.path.expanduser("~/.config/chromium")  # Expanding the ~ for Linux

# If you have a specific profile (e.g., Profile 1, Profile 2), specify it here.
PROFILE_DIRECTORY = "Profile 3"

# Path to your ChromeDriver executable
CHROMEDRIVER_PATH = os.path.join(os.path.dirname(__file__), "chromedriver-linux64/chromedriver")

def extract_tweet_id(url):
    """Extract tweet ID from URL"""
    match = re.search(r'/status/(\d+)', url)
    return match.group(1) if match else None

def convert_to_local_time(utc_timestamp):
    """Convert UTC timestamp to UTC+8 and format it"""
    # Parse the ISO format timestamp
    utc_time = datetime.fromisoformat(utc_timestamp.replace('Z', '+00:00'))
    # Convert to UTC+8 time
    local_time = utc_time.astimezone(ZoneInfo('Asia/Shanghai'))
    # Format as desired
    return local_time.strftime('%Y-%m-%d %H:%M:%S')

def extract_tweet_data(driver, wait, tweet_id):
    """Extract tweet data including text, author, metrics etc."""
    tweet_data = {}
    
    try:
        # Store tweet ID
        tweet_data['tweet_id'] = tweet_id
        
        # Wait for tweet content to load
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="tweetText"]')))
        
        # Get tweet text
        tweet_text_elem = driver.find_element(By.CSS_SELECTOR, '[data-testid="tweetText"]')
        tweet_data['text'] = tweet_text_elem.text

        # Get author info - separate display name and handle
        try:
            author_container = driver.find_element(By.CSS_SELECTOR, '[data-testid="User-Name"]')
            display_name = author_container.find_element(By.CSS_SELECTOR, 'span span').text
            handle = author_container.find_element(By.CSS_SELECTOR, 'div[dir="ltr"]').text
            tweet_data['author'] = {
                'name': display_name,
                'handle': handle
            }
        except Exception as e:
            print(f"Error extracting author info: {e}")
            tweet_data['author'] = {'name': '', 'handle': ''}

        # Get timestamp and convert to UTC+8 time
        time_elem = driver.find_element(By.CSS_SELECTOR, 'time')
        utc_timestamp = time_elem.get_attribute('datetime')
        tweet_data['timestamp'] = convert_to_local_time(utc_timestamp)

        # Get engagement metrics
        metrics = {}
        metric_elements = driver.find_elements(By.CSS_SELECTOR, '[data-testid="app-text-transition-container"]')
        if len(metric_elements) >= 5:
            metrics['views'] = metric_elements[0].text
            metrics['replies'] = metric_elements[1].text
            metrics['reposts'] = metric_elements[2].text
            metrics['likes'] = metric_elements[3].text
            metrics['bookmarks'] = metric_elements[4].text
        tweet_data['metrics'] = metrics

        # Get any media
        try:
            media_container = driver.find_element(By.CSS_SELECTOR, '[data-testid="tweetPhoto"]')
            tweet_data['has_media'] = True
            media_elements = media_container.find_elements(By.TAG_NAME, 'img')
            tweet_data['media_urls'] = [img.get_attribute('src') for img in media_elements]
        except:
            tweet_data['has_media'] = False
            tweet_data['media_urls'] = []

        tweet_data['crawl_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        return tweet_data

    except Exception as e:
        print(f"Error extracting tweet data: {e}")
        return None

def scroll_to_load_replies(driver, wait, max_scrolls=100):
    """Scroll down the page to load more replies"""
    last_height = driver.execute_script("return document.body.scrollHeight")
    scrolls = 0
    
    while scrolls < max_scrolls:
        # Scroll down
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(3)  # Wait for content to load
        
        # Calculate new scroll height
        new_height = driver.execute_script("return document.body.scrollHeight")
        
        # Break if no more new content
        if new_height == last_height:
            break
            
        last_height = new_height
        scrolls += 1

def extract_reply_data(driver, wait):
    """Extract reply data including text, author, and timestamp"""
    replies = []
    processed_texts = set()  # Keep track of processed replies to avoid duplicates
    
    def safe_find_element(container, selector, multiple=False):
        """Safely find element(s) with retry logic for stale elements"""
        max_retries = 3
        for _ in range(max_retries):
            try:
                if multiple:
                    return container.find_elements(By.CSS_SELECTOR, selector)
                return container.find_element(By.CSS_SELECTOR, selector)
            except Exception:
                if _ == max_retries - 1:  # Last retry
                    return [] if multiple else None
                time.sleep(0.5)
        return [] if multiple else None

    try:
        # Wait for replies to load
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')))
        
        last_height = driver.execute_script("return document.body.scrollHeight")
        max_scrolls = 100  # Maximum number of scrolls to prevent infinite loops
        scrolls = 0
        
        while scrolls < max_scrolls:
            # Find all currently visible reply containers
            reply_containers = driver.find_elements(By.CSS_SELECTOR, '[data-testid="cellInnerDiv"]')
            
            # Skip the first container as it's usually the original tweet
            for container in reply_containers[1:]:
                try:
                    # Check if this is actually a reply container
                    if not safe_find_element(container, '[data-testid="tweetText"], [data-testid="User-Name"]', multiple=True):
                        continue
                    
                    reply_data = {}
                    
                    # Get reply text
                    reply_text_elems = safe_find_element(container, '[data-testid="tweetText"]', multiple=True)
                    if not reply_text_elems:
                        continue
                        
                    text = reply_text_elems[0].text
                    # Skip if we've already processed this reply
                    if not text or text in processed_texts:
                        continue
                    reply_data['text'] = text
                    processed_texts.add(text)

                    # Get author info
                    author_container = safe_find_element(container, '[data-testid="User-Name"]')
                    if not author_container:
                        continue
                        
                    name_elem = safe_find_element(author_container, 'span span', multiple=True)
                    handle_elem = safe_find_element(author_container, 'div[dir="ltr"]', multiple=True)
                    
                    if name_elem and handle_elem:
                        reply_data['author'] = {
                            'name': name_elem[0].text,
                            'handle': handle_elem[0].text
                        }
                    else:
                        continue

                    # Get timestamp
                    time_elem = safe_find_element(container, 'time')
                    if not time_elem:
                        continue
                        
                    utc_timestamp = time_elem.get_attribute('datetime')
                    if not utc_timestamp:
                        continue
                    reply_data['timestamp'] = convert_to_local_time(utc_timestamp)

                    # Only append if we have all required data
                    if all(key in reply_data for key in ['text', 'author', 'timestamp']) and \
                       reply_data['text'] and reply_data['author']['name'] and reply_data['author']['handle']:
                        replies.append(reply_data)
                        print(f"\rReplies extracted: {len(replies)}", end="", flush=True)

                except Exception as e:
                    if "stale element reference" not in str(e):
                        print(f"\nError processing individual reply: {e}")
                    continue
            
            # Scroll down
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)  # Wait for content to load
            
            # Calculate new scroll height
            new_height = driver.execute_script("return document.body.scrollHeight")
            
            # Break if no more new content
            if new_height == last_height:
                break
                
            last_height = new_height
            scrolls += 1

        print(f"\nSuccessfully extracted {len(replies)} valid replies")
        return replies

    except Exception as e:
        print(f"Error extracting replies: {e}")
        return []

def crawl_reply_by_id(tweet_id: str, existing_driver=None, existing_wait=None):
    """
    Crawl replies for a tweet by its ID and return the extracted data
    
    Args:
        tweet_id (str): The ID of the tweet to crawl replies from
        existing_driver: Optional existing webdriver instance (deprecated)
        existing_wait: Optional existing WebDriverWait instance (deprecated)
        
    Returns:
        list: List of reply data dictionaries or empty list if extraction fails
    """
    try:
        # Get or initialize browser
        driver, wait = TwitterCrawler.get_browser()
        
        # Navigate to the tweet
        target_url = f'https://x.com/i/web/status/{tweet_id}'
        current_url = driver.current_url
        
        # Only navigate if we're not already on the correct page
        if current_url != target_url:
            driver.get(target_url)
            time.sleep(5)  # Wait for content to load
        
        # Extract reply data
        replies = extract_reply_data(driver, wait)
        
        if replies:
            # Remove last 4 (Discover More)
            if len(replies) > 4:
                replies = replies[:-4]
                
            # Create replies directory if it doesn't exist
            replies_dir = os.path.join(os.path.dirname(__file__), 'replies')
            os.makedirs(replies_dir, exist_ok=True)
            
            # Save to JSON file using tweet ID
            json_path = os.path.join(replies_dir, f'replies_{tweet_id}.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(replies, f, ensure_ascii=False, indent=4)
            print(f"Reply data saved to {json_path}")
            print(f"\nExtracted {len(replies)} replies")
            return replies
        else:
            print("No valid replies found")
            return []

    except Exception as e:
        print(f"An error occurred: {e}")
        return []

def crawl_twitter(url: str, need_reply: bool = False):
    """
    Crawl a Twitter/X post and its replies given a URL.
    
    Args:
        url (str): The URL of the tweet to crawl
        need_reply (bool, optional): Whether to crawl replies. Defaults to True.
        
    Returns:
        tuple: (tweet_data, replies) where tweet_data is a dict of the original tweet
               and replies is a list of reply data dictionaries
    """
    # Extract tweet ID from URL
    tweet_id = ''
    if '.com' in url:
        tweet_id = extract_tweet_id(url)
    else:
        tweet_id = url
    
    if not tweet_id:
        print("Could not extract tweet ID from URL")
        return None, []

    tweet_data = None
    replies = []
    
    try:
        # Get or initialize browser
        driver, wait = TwitterCrawler.get_browser()
        
        # Crawl the tweet
        print("Crawling original tweet...")
        url = f'https://x.com/i/web/status/{tweet_id}'
        driver.get(url)
        tweet_data = extract_tweet_data(driver, wait, tweet_id)
        
        if tweet_data:
            # Save tweet data
            tweets_dir = os.path.join(os.path.dirname(__file__), 'tweets')
            os.makedirs(tweets_dir, exist_ok=True)
            json_path = os.path.join(tweets_dir, f'tweet_{tweet_id}.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(tweet_data, f, ensure_ascii=False, indent=4)
            print(f'tweet_data: {tweet_data}')
            print(f"Tweet data saved to {json_path}")
        else:
            print("Failed to crawl tweet")
            
        # Only crawl replies if need_reply is True
        if need_reply:
            print("\nCrawling replies...")
            replies = crawl_reply_by_id(tweet_id)
            if replies:
                print(f"Successfully crawled {len(replies)} replies")
            else:
                print("No replies found or failed to crawl replies")
            
    except Exception as e:
        print(f"An error occurred: {e}")
    
    return tweet_data, replies

def main():
    try:
        # Example usage
        example_url = "https://x.com/sysxplore/status/1930336144102727793"
        tweet_data, replies = crawl_twitter(example_url, need_reply=True)

        another_url = 'https://x.com/JonErlichman/status/1936560289752748491'
        tweet_data, replies = crawl_twitter(another_url, need_reply=True)

    finally:
        # Clean up browser when done
        TwitterCrawler.close_browser()

if __name__ == "__main__":
    main()
