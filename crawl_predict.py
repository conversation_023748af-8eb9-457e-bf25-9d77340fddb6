import sys
import os
import json
import requests
import datetime
import random

from crawl_repost import extract_weibo_id, crawl_weibo
from WeiboSpider.output.reformat import reformat_to_csv
from WeiboSpider.output.sort import sort_csv_data
from bert_emo.text2emo import text2emo
from cas3d.process_and_predict import predict_cascade
from cas3d.process_and_predict import predict_cascade_twitter
import cas3d.process_and_predict as process_and_predict

from twitterSelenium.crawl_twitter import extract_tweet_id
from twitterSelenium.crawl_twitter import crawl_twitter


def get_result_weibo(tweet_id: str, refresh: bool = False):
    result = {}
    result['tweet_id'] = tweet_id

    output_dir = f"WeiboSpider/output"
    if refresh:
        print(f'crawling weibo {tweet_id} with refresh')
        crawl_weibo(tweet_id)
    elif not os.path.exists(f"{output_dir}/{tweet_id}_tweet.jsonl") or not os.path.exists(f"{output_dir}/{tweet_id}_repost.jsonl") or not os.path.exists(f"{output_dir}/{tweet_id}_comment.jsonl"):
        print(f'crawled file {output_dir}/{tweet_id}_tweet.jsonl or {output_dir}/{tweet_id}_repost.jsonl or {output_dir}/{tweet_id}_comment.jsonl not found.')
        crawl_weibo(tweet_id)
    else:
        print(f'refresh = {refresh}, crawled file {output_dir}/{tweet_id}_tweet.jsonl and {output_dir}/{tweet_id}_repost.jsonl found.')

    # 推文内容
    with open(f"{output_dir}/{tweet_id}_tweet.jsonl", "r") as tweet_file:
        first_line = tweet_file.readline()
        tweet_data = json.loads(first_line)
        print("Crawl tweet content: ", tweet_data['content'])
        print("Created at: ", tweet_data['created_at'])
        print("Author: ", tweet_data['user']['nick_name'])
        print("reposts count: ", tweet_data['reposts_count'])
        print("comments count: ", tweet_data['comments_count'])
        print("attitudes count: ", tweet_data['attitudes_count'])

        result['tweet_content'] = tweet_data['content']
        result['created_at'] = tweet_data['created_at']
        result['author'] = tweet_data['user']['nick_name']
        result['reposts_count'] = tweet_data['reposts_count']
        result['comments_count'] = tweet_data['comments_count']
        result['attitudes_count'] = tweet_data['attitudes_count']

    # 转发
    repost_lines = 0
    with open(f"{output_dir}/{tweet_id}_repost.jsonl", "r") as repost_file:
        for _ in repost_file:
            repost_lines += 1

    if not os.path.exists(f"{output_dir}/{tweet_id}_repost.csv"):
        reformat_to_csv(tweet_id)
    
    if not os.path.exists(f"{output_dir}/{tweet_id}_repost_sorted.csv"):
        sort_csv_data(tweet_id)

    # 评论
    comment_lines = 0
    with open(f"{output_dir}/{tweet_id}_comment.jsonl", "r") as comment_file:
        for _ in comment_file:
            comment_lines += 1



    # 情感预测
    text = tweet_data['content']
    if len(text) > 512:
        text = text[:512]
    emo_prediction = text2emo(text)
    result['emo_label'] = emo_prediction[0]['label']
    result['emo_score'] = f"{emo_prediction[0]['score'] * 100:.1f}%"
    print("Emo prediction: ", emo_prediction)

    # 转发级联预测
    repost_ratio = result['reposts_count'] / repost_lines
    print(f'repost ratio: {repost_ratio}')
    repost_prediction = predict_cascade(tweet_id)

    formatted_data = format_weibo_reposts(tweet_id)
    # 推特或微博id，发布时间，爬取时间，总数，一小时路径/转发时间列表，标签
    save2dataset(tweet_id, result['created_at'], tweet_data['crawl_time'], result['reposts_count'], formatted_data, 'weibo_reposts')

    
    result['repost_predicted_delta'] = int(repost_prediction['predicted_delta'] * repost_ratio)
    result['repost_observed_count'] = int(repost_prediction['observed_count'] * repost_ratio)
    result['repost_total_prediction'] = int(repost_prediction['total_prediction'] * repost_ratio)
    result['repost_true_num'] = int(repost_prediction['true_num'] * repost_ratio)
    # prediction should not less then reposts now, if so, add it with delta
    # in this situation, the true num should equals to reposts_count
    # so if reposts now > true_num, these is no need to fix it
    if result['reposts_count'] <= result['repost_true_num'] and result['repost_total_prediction'] < result['reposts_count']:
        result['repost_total_prediction'] = result['reposts_count'] + result['repost_predicted_delta']

    # 评论级联预测
    comment_ratio = result['comments_count'] / comment_lines
    print(f'comment ratio: {comment_ratio}')
    formatted_comments = format_weibo_comments(tweet_id)
    comment_prediction = predict_cascade_twitter(formatted_comments)

    # 推特或微博id，发布时间，爬取时间，总数，一小时路径/转发时间列表，标签
    save2dataset(tweet_id, result['created_at'], tweet_data['crawl_time'], result['comments_count'], formatted_comments, 'weibo_replies')

    result['comment_predicted_delta'] = int(comment_prediction['predicted_delta'] * comment_ratio)
    result['comment_observed_count'] = int(comment_prediction['observed_count'] * comment_ratio)
    result['comment_total_prediction'] = int(comment_prediction['total_prediction'] * comment_ratio)
    result['comment_true_num'] = int(comment_prediction['true_num'] * comment_ratio)

    if result['comments_count'] <= result['comment_true_num'] and result['comment_total_prediction'] < result['comments_count']:
        result['comment_total_prediction'] = result['comments_count'] + result['comment_predicted_delta']



    data = {}
    data['tweet_id'] = tweet_id
    data['created_at'] = datetime.datetime.strptime(tweet_data['created_at'], '%Y-%m-%d %H:%M:%S')
    data['pred_time'] = datetime.datetime.now()
    data['crawl_time'] = datetime.datetime.fromtimestamp(tweet_data['crawl_time'])
    data['reposts_count'] = tweet_data['reposts_count']
    data['comments_count'] = tweet_data['comments_count']
    data['attitudes_count'] = tweet_data['attitudes_count']
    data['user_id'] = tweet_data['user']['_id']
    data['username'] = tweet_data['user']['nick_name']
    data['emo_text'] = result['emo_label']
    data['emo_score'] = result['emo_score']
    
    # 转发
    data['repost_one_hour_number'] = result['repost_observed_count']
    data['repost_pred_num'] = result['repost_total_prediction']
    if (data['crawl_time'] - data['created_at']).total_seconds() < 3600 * 24:
        data['repost_true_num'] = -1
    else:
        data['repost_true_num'] = result['repost_true_num']

    # 评论
    data['comment_one_hour_number'] = result['comment_observed_count']
    data['comment_pred_num'] = result['comment_total_prediction']
    if (data['crawl_time'] - data['created_at']).total_seconds() < 3600 * 24:
        data['comment_true_num'] = -1
    else:
        data['comment_true_num'] = result['comment_true_num']
    

    data['source'] = 0
    save2db(data)
    return result


def get_result_twitter(tweet_id: str, refresh: bool = False):
    print(f'tweet_id: {tweet_id}')
    result = {}
    result['tweet_id'] = tweet_id

    tweet_data = {}
    replies = []
    
    output_dir = 'twitterSelenium'
    tweets_file = f"{output_dir}/tweets/tweet_{tweet_id}.json"
    replies_file = f"{output_dir}/replies/replies_{tweet_id}.json"

    if refresh:
        print(f'crawling twitter {tweet_id} with refresh')
        tweet_data, replies = crawl_twitter(tweet_id, need_reply=True)
    elif not os.path.exists(tweets_file) or not os.path.exists(replies_file):
        print(f'crawled file {tweets_file} and {replies_file} not found.')
        tweet_data, replies = crawl_twitter(tweet_id, need_reply=True)
    else:
        tweet_data = json.load(open(tweets_file))
        replies = json.load(open(replies_file))
        print(f'crawled file {tweets_file} found. {tweet_data}')
        print(f'crawled file {replies_file} found. {len(replies)} replies')

    result['tweet_content'] = tweet_data['text']
    result['created_at'] = tweet_data['timestamp']
    result['author'] = tweet_data['author']['handle']
    result['reposts_count'] = convert_count_to_int(tweet_data['metrics']['reposts'])
    result['comments_count'] = convert_count_to_int(tweet_data['metrics']['replies'])
    result['attitudes_count'] = convert_count_to_int(tweet_data['metrics']['likes'])

    print("Crawl tweet content: ", result['tweet_content'])
    print("Created at: ", result['created_at'])
    print("Author: ", result['author'])
    print("reposts count: ", result['reposts_count'])
    print("comments count: ", result['comments_count'])
    print("attitudes count: ", result['attitudes_count'])

    # 情感预测
    text = result['tweet_content']
    if len(text) > 512:
        text = text[:512]
    emo_prediction = text2emo(text)
    result['emo_label'] = emo_prediction[0]['label']
    result['emo_score'] = f"{emo_prediction[0]['score'] * 100:.1f}%"
    print("Emo prediction: ", emo_prediction)

    # 级联预测
    # 对于推特来说是评论数
    comment_ratio = result['comments_count'] / len(replies)
    print(f'comment ratio: {comment_ratio}')
    formatted_replies = format_replies(replies, result['created_at'])

    # 推特或微博id，发布时间，爬取时间，总数，一小时路径/转发时间列表，标签
    save2dataset(tweet_id, result['created_at'], tweet_data['crawl_time'], result['comments_count'], formatted_replies, 'tweet_replies')

    prediction = predict_cascade_twitter(formatted_replies)
    result['comment_predicted_delta'] = int(prediction['predicted_delta'] * comment_ratio)
    result['comment_observed_count'] = int(prediction['observed_count'] * comment_ratio)
    result['comment_total_prediction'] = int(prediction['total_prediction'] * comment_ratio)
    result['comment_true_num'] = int(prediction['true_num'] * comment_ratio)
    # prediction should not less then reposts now, if so, add it with delta
    # in this situation, the true num should equals to reposts_count
    # so if reposts now > true_num, these is no need to fix it
    if result['comments_count'] <= result['comment_true_num'] and result['comment_total_prediction'] < result['comments_count']:
        result['comment_total_prediction'] = result['comments_count'] + result['comment_predicted_delta']
    

    data = {}
    data['tweet_id'] = tweet_id
    data['created_at'] = datetime.datetime.strptime(result['created_at'], '%Y-%m-%d %H:%M:%S')
    data['pred_time'] = datetime.datetime.now()
    data['crawl_time'] = datetime.datetime.strptime(tweet_data['crawl_time'], '%Y-%m-%d %H:%M:%S')
    data['reposts_count'] = result['reposts_count']
    data['comments_count'] = result['comments_count']
    data['attitudes_count'] = result['attitudes_count']
    # 推特的唯一用户名作为unique_id
    data['user_id'] = tweet_data['author']['handle'][:min(10, len(tweet_data['author']['handle']))]
    data['username'] = tweet_data['author']['name']
    data['emo_text'] = result['emo_label']
    data['emo_score'] = result['emo_score']

    data['comment_one_hour_number'] = result['comment_observed_count']
    data['comment_pred_num'] = result['comment_total_prediction']
    if (data['crawl_time'] - data['created_at']).total_seconds() < 3600 * 24:
        data['comment_true_num'] = -1
    else:
        data['comment_true_num'] = result['comment_true_num']

    data['repost_one_hour_number'] = -1
    data['repost_pred_num'] = -1
    data['repost_true_num'] = -1

    data['source'] = 1
    save2db(data)
    return result



def crawl_and_predict(url_or_id: str, refresh: bool = False):
    if ".com" in url_or_id:
        tweet_id = extract_weibo_id(url_or_id)
        if tweet_id is not None:
            print(f"Extracted Weibo tweet ID: {tweet_id}")
            return get_result_weibo(tweet_id, refresh)
        else:
            tweet_id = extract_tweet_id(url_or_id)
            print(f"Extracted Twitter tweet ID: {tweet_id}")
            return get_result_twitter(tweet_id, refresh)
    else:
        url_or_id = url_or_id.strip()
        if url_or_id.isdigit():
            print(f"Extracted Twitter tweet ID: {url_or_id}")
            return get_result_twitter(url_or_id, refresh)
        else:
            print(f"Extracted Weibo tweet ID: {url_or_id}")
            return get_result_weibo(url_or_id, refresh)


def save2db(data: dict):
    # Convert datetime objects to strings for JSON serialization
    data_to_send = data.copy()
    data_to_send['pred_time'] = data['pred_time'].strftime('%Y-%m-%d %H:%M:%S')
    data_to_send['crawl_time'] = data['crawl_time'].strftime('%Y-%m-%d %H:%M:%S')
    data_to_send['created_at'] = data['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    
    try:
        response = requests.put('http://localhost:8080/api/history', json=data_to_send)
        response.raise_for_status()
        print("Data successfully saved to database")
    except requests.exceptions.RequestException as e:
        print(f"Error saving to database: {str(e)}")


# 推特或微博id，发布时间，爬取时间，总数，一小时路径/转发时间列表，标签
def save2dataset(tweet_id: str, created_at: str, crawl_time: str, count: int, formatted_replies: str, tag: str):
    import csv

    dir = os.path.dirname(os.path.abspath(__file__)) + '/dataset'
    os.makedirs(dir, exist_ok=True)

    # Generate date string in format YYYYMMDD from current date
    current_date = datetime.datetime.now().strftime('%Y%m%d')
    csv_file = os.path.join(dir, f"{tag}_{current_date}.csv")

    # Convert timestamps to unix int timestamp
    created_at_unix = int(datetime.datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S').timestamp())

    # Handle crawl_time which might already be a unix timestamp or a string
    if isinstance(crawl_time, str):
        crawl_time_unix = int(datetime.datetime.strptime(crawl_time, '%Y-%m-%d %H:%M:%S').timestamp())
    else:
        crawl_time_unix = int(crawl_time)

    if crawl_time_unix - created_at_unix < 3600 * 24:
        print(f"time gap is less than 24 hours, not saving to dataset")
        return

    # Check if file exists and has headers
    file_exists = os.path.exists(csv_file)
    has_headers = False
    existing_ids = set()

    if file_exists:
        # Read all existing content to check for duplicate tweet_id and headers
        try:
            with open(csv_file, 'r', newline='', encoding='utf-8') as f:
                reader = csv.reader(f)
                first_row = next(reader, None)
                if first_row and first_row == ['id', 'created_at', 'count', 'path']:
                    has_headers = True
                    # Read all existing IDs
                    for row in reader:
                        if row:  # Skip empty rows
                            existing_ids.add(row[0])  # First column is the ID
                else:
                    # If no headers, treat first row as data
                    if first_row:
                        existing_ids.add(first_row[0])
                    # Read remaining rows
                    for row in reader:
                        if row:  # Skip empty rows
                            existing_ids.add(row[0])
        except (IOError, StopIteration):
            has_headers = False

    # Check if tweet_id already exists
    if tweet_id in existing_ids:
        print(f"Tweet ID {tweet_id} already exists in {csv_file}, skipping")
        return

    # Prepare data row
    data_row = [tweet_id, created_at_unix, count, formatted_replies]

    # Write to CSV file
    with open(csv_file, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)

        # If file doesn't exist or doesn't have headers, write headers first
        if not file_exists or not has_headers:
            writer.writerow(['id', 'created_at', 'count', 'path'])

        # Write the data row
        writer.writerow(data_row)

    print(f"Data saved to {csv_file}")




def convert_count_to_int(count_str):
    if isinstance(count_str, (int, float)):
        return int(count_str)
    
    count_str = str(count_str).strip().upper()
    if count_str.endswith('K'):
        return int(float(count_str[:-1]) * 1000)
    elif count_str.endswith('M'):
        return int(float(count_str[:-1]) * 1000000)
    else:
        return int(float(count_str))

def format_replies(replies: list, tweet_timestamp: str, only_one_hour: bool = False) -> str:
    formatted_replies = []
    tweet_dt = datetime.datetime.strptime(tweet_timestamp, '%Y-%m-%d %H:%M:%S')
    tweet_unix = int(tweet_dt.timestamp())
    
    # Create list of (time_gap, formatted_string) tuples
    reply_data = []
    for reply in replies:
        random_id = random.randint(1000000000, 9999999999)
        reply_dt = datetime.datetime.strptime(reply['timestamp'], '%Y-%m-%d %H:%M:%S')
        reply_unix = int(reply_dt.timestamp())
        time_gap = reply_unix - tweet_unix
        if time_gap < 0:
            raise Exception(f"get negative time gap: {time_gap}, created_at: {tweet_timestamp}, reply: {reply['timestamp']}, content: {reply['text']}")
        elif only_one_hour and time_gap > 3600:
            continue
        formatted_reply = f"0/{random_id}:{time_gap}"
        reply_data.append((time_gap, formatted_reply))
    

    # Sort by time_gap and extract only the formatted strings
    reply_data.sort(key=lambda x: x[0])
    formatted_replies = [data[1] for data in reply_data]
    
    return ' '.join(formatted_replies)

def format_weibo_comments(tweet_id: str, only_one_hour: bool = False) -> str:
    formatted_comments = []
    output_dir = "WeiboSpider/output"
    
    # Read the original tweet to get its timestamp
    with open(f"{output_dir}/{tweet_id}_tweet.jsonl", "r") as tweet_file:
        first_line = tweet_file.readline()
        tweet_data = json.loads(first_line)
        tweet_dt = datetime.datetime.strptime(tweet_data['created_at'], '%Y-%m-%d %H:%M:%S')
        tweet_unix = int(tweet_dt.timestamp())
    
    # Create list of (time_gap, formatted_string) tuples
    comment_data = []
    with open(f"{output_dir}/{tweet_id}_comment.jsonl", "r") as comment_file:
        for line in comment_file:
            comment = json.loads(line)
            comment_dt = datetime.datetime.strptime(comment['created_at'], '%Y-%m-%d %H:%M:%S')
            comment_unix = int(comment_dt.timestamp())
            time_gap = comment_unix - tweet_unix
            if time_gap < 0:
                raise Exception(f"get negative time gap: {time_gap}, created_at: {tweet_data['created_at']}, reply: {comment['created_at']}, content: {comment['text']}")
            elif only_one_hour and time_gap > 3600:
                continue
            formatted_comment = f"0/{comment['_id']}:{time_gap}"
            comment_data.append((time_gap, formatted_comment))
    
    # Sort by time_gap and extract only the formatted strings
    comment_data.sort(key=lambda x: x[0])
    formatted_comments = [data[1] for data in comment_data]
    
    return ' '.join(formatted_comments)

def format_weibo_reposts(tweet_id: str) -> str:
    import csv

    formatted_reposts = []
    output_dir = "WeiboSpider/output"
    csv_file = f"{output_dir}/{tweet_id}_repost_sorted.csv"

    # Check if file exists and throw error if not
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"Repost CSV file not found: {csv_file}")

    # Read the CSV file and process each line
    with open(csv_file, "r", encoding='utf-8') as sorted_csv:
        reader = csv.DictReader(sorted_csv)

        for row in reader:
            repost_time = int(row['repost_time'])

            # Stop processing if repost_time > 3600
            if repost_time > 3600:
                break

            cas_path = row['cas_path']
            formatted_entry = f"{cas_path}:{repost_time}"
            formatted_reposts.append(formatted_entry)

    return ' '.join(formatted_reposts)

def main():
    if len(sys.argv) != 2:
        print("Usage: python crawl_repost.py <weibo_url_or_id>")
        sys.exit(1)
    
    crawl_and_predict(sys.argv[1], True)

if __name__ == "__main__":
    main()