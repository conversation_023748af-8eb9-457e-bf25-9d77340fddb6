import pandas as pd
import datetime
from collections import defaultdict
import os
import json

def process_csv_data(tweet_id):
    # Get the directory of the current script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    # Construct the file path relative to the script location
    csv_file = os.path.abspath(os.path.join(script_dir, '../WeiboSpider/output', f'{tweet_id}_repost_sorted.csv'))
    
    # Load the CSV file
    df = pd.read_csv(csv_file)
    
    # Check if the crawl_time from start is greater than 3600
    tweet_file_name = os.path.abspath(os.path.join(script_dir, '../WeiboSpider/output', f'{tweet_id}_tweet.jsonl'))
    repost_file_name = os.path.abspath(os.path.join(script_dir, '../WeiboSpider/output', f'{tweet_id}_repost.jsonl'))
    with open(tweet_file_name, 'r') as tweet_file, open(repost_file_name, 'r') as repost_file:
        first_line = tweet_file.readline()
        tweet_data = json.loads(first_line)
        created_at_str = tweet_data['created_at']
        created_at = int(datetime.datetime.strptime(created_at_str, "%Y-%m-%d %H:%M:%S").timestamp())
        
        first_repost_data = json.loads(repost_file.readline())  # Read first line of repost file
        crawl_time = first_repost_data['crawl_time']
        gap = crawl_time - created_at
    
        if gap <= 3600:
            raise Exception("距离帖文发布的时间应该大于 3600")
    
    # Group by root ID to process each cascade
    cascades = defaultdict(list)
    
    for _, row in df.iterrows():
        # Split the cascade path to get components
        path_components = row['cas_path'].split('/')
        root_id = path_components[0]  # The first component is the root
        time_seconds = int(row['repost_time'])  # Assuming 'repost_time' is the column for time
        
        # Add root post with time 0
        if len(path_components) == 1:
            path = f"{root_id}:0"
            if path not in cascades[root_id]:
                cascades[root_id].append(path)
        else:
            # For non-root posts, add the full path with timestamp
            path = f"{'/'.join(path_components)}:{time_seconds}"
            cascades[root_id].append(path)
    
    return cascades

def format_for_inference(cascades):
    """Format data in a single line format like "944:0 944/945:84640 944/946:83111" """
    formatted_data = []
    
    for root_id, paths in cascades.items():
        # Sort paths by time (keeping root post with time 0 first)
        paths.sort(key=lambda x: int(x.split(':')[1]))
        
        # Join all paths with spaces
        cascade_line = ' '.join(paths)
        formatted_data.append(cascade_line)
    
    return formatted_data

def predict_cascade(tweet_id):
    from .inference import Cas3DPredictor

    # Process the CSV data
    print("Processing CSV data...")
    cascades = process_csv_data(tweet_id)

    # Format data for inference
    print("Formatting data for inference...")
    formatted_data = format_for_inference(cascades)

    # Initialize predictor
    print("\nMaking predictions:")
    print("-" * 50)

    predictor = Cas3DPredictor()
    for cascade_line in formatted_data:
        predicted_delta, observed_count, total_prediction, true_num = predictor.predict(cascade_line)
        print(f"Observed Retweets: {observed_count}")
        print(f"Predicted Additional Retweets: {predicted_delta:.2f}")
        print(f"Total Predicted Retweets: {total_prediction:.2f}")
        print(f"True Retweets: {true_num}")
        print("-" * 30)

    return {"predicted_delta": predicted_delta, "observed_count": observed_count, "total_prediction": total_prediction, "true_num": true_num}


def predict_cascade_twitter(formatted_replies):
    from .inference import Cas3DPredictor

    # Initialize predictor
    print("\nMaking predictions:")
    print("-" * 50)

    predictor = Cas3DPredictor()
    predicted_delta, observed_count, total_prediction, true_num = predictor.predict(formatted_replies)
    print(f"Observed Retweets: {observed_count}")
    print(f"Predicted Additional Retweets: {predicted_delta:.2f}")
    print(f"Total Predicted Retweets: {total_prediction:.2f}")
    print(f"True Retweets: {true_num}")
    print("-" * 30)

    return {"predicted_delta": predicted_delta, "observed_count": observed_count, "total_prediction": total_prediction, "true_num": true_num}

# Optionally, keep a minimal main stub for CLI usage
if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        predict_cascade(sys.argv[1])
    else:
        print("Usage: python process_data.py <tweet_id>") 