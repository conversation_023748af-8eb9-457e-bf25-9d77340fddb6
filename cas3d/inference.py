import torch
from .model import Cas3D
from .config import Config
import numpy as np
import time
import os

class Cas3DPredictor:
    def __init__(self, model_path=None, config=None):
        if model_path is None:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(script_dir, 'model_state_dict.pth')
        self.model_path = model_path
        # If no config is provided, use default config for inference
        if config is None:
            config = Config(
                dataset='weibo',  # default dataset
                pred_time=24*3600,
                observation_time=1*3600,
                time_interval=36,
                hidden_size=64,
                num_layers=2,
                mlp=[64, 32, 1],
                lr=0.005,
                batch_size=1  # batch size 1 for inference
            )
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")
        
        self.model = Cas3D(config)
        # Load state dict with weights_only=True for security and to avoid warning
        state_dict = torch.load(model_path, map_location=self.device, weights_only=True)
        self.model.load_state_dict(state_dict)
        self.model = self.model.to(self.device)
        self.model.eval()  # Set to evaluation mode
    
    def process_cascade(self, cascade_path):
        """
        Process a cascade path into model input
        
        Args:
            cascade_path (str): A cascade path string in format:
                "root:0 root/user1:time1 root/user2:time2 ..."
        
        Returns:
            torch.Tensor: Processed input tensor for the model
        """
        # Initialize input tensor
        x = [[0 for _ in range(self.config.Maxdepth-2)] for _ in range(self.config.time_interval_num)]
        
        nodes_time = dict()
        nodes_time['-1'] = 0
        
        paths = cascade_path.strip().split(' ')
        
        for p in paths:
            nodes = p.split(':')[0].split('/')
            time_now = int(p.split(":")[1])
            
            nodes_time[nodes[-1]] = time_now
            
            if len(nodes) == 1:  # root node
                continue
                
            if time_now < self.config.observation_time:
                # If the diffusion depth is greater than the maximum
                if len(nodes) > self.config.Maxdepth:
                    depth_index = self.config.Maxdepth-2
                else:
                    # DDD starts at 2, and the index of the diffusion depth equal to 2 is 0
                    depth_index = len(nodes)-2
                
                time_now_index = int(time_now//self.config.time_interval)
                x[time_now_index][depth_index] += 1
        
        # Convert to tensor and move to device
        x = torch.tensor(x, dtype=torch.float).to(self.device)
        return x.unsqueeze(0)  # Add batch dimension
    
    def count_observed_retweets(self, cascade_path):
        """
        Count the number of retweets within observation time
        
        Args:
            cascade_path (str): A cascade path string
        
        Returns:
            int: Number of retweets within observation time
        """
        paths = cascade_path.strip().split(' ')
        
        observed_count = 0
        true_num = 0
        for p in paths:
            nodes = p.split(':')[0].split('/')
            time_now = int(p.split(":")[1])
            
            if time_now < self.config.observation_time and len(nodes) > 1:  # Skip root node
                observed_count += 1

            if time_now < 3600 * 24 and len(nodes) > 1:
                true_num += 1
        
        return observed_count, true_num
    
    def predict(self, cascade_path):
        """
        Make predictions using the loaded model
        
        Args:
            cascade_path (str): A cascade path string in format:
                "root:0 root/user1:time1 root/user2:time2 ..."
        
        Returns:
            tuple: (predicted_delta, observed_count, total_prediction)
        """
        with torch.no_grad():
            # Process the input
            input_data = self.process_cascade(cascade_path)
            
            # Get raw predictions
            predictions = self.model(input_data)
            
            # Apply the same transformation as in training
            predictions[predictions < 0] = 0
            predictions = torch.pow(2, predictions).sub(1)
            predictions[predictions < 1] = 1
            
            # Count observed retweets
            observed_count, true_num = self.count_observed_retweets(cascade_path)
            
            # Calculate total prediction
            predicted_delta = predictions.cpu().numpy()[0][0]
            total_prediction = observed_count + predicted_delta
            
            return predicted_delta, observed_count, total_prediction, true_num

def process_dataset_line(line):
    """Convert a dataset line to cascade path format"""
    parts = line.strip().split('\t')
    message_id = parts[0]
    user_id = parts[1]
    publish_time = parts[2]
    retweets = parts[4]
    
    # Add root node with time 0
    cascade_path = f"{user_id}:0 {retweets}"
    return cascade_path, int(parts[3])  # Return path and actual retweet count

def main():
    # Specify which line to analyze (1-based index)
    line_to_analyze = 10  # Change this number to analyze different lines
    
    # Example usage
    predictor = Cas3DPredictor()
    
    # Read the specified line from the dataset
    with open('data/weibo/dataset.txt', 'r') as f:
        for i, line in enumerate(f, 1):
            if i == line_to_analyze:
                # Convert dataset line to cascade path format
                cascade_path, actual_retweets = process_dataset_line(line)
                break
        else:
            print(f"Error: Line {line_to_analyze} not found in dataset")
            return
    
    # Get prediction
    predicted_delta, observed_count, total_prediction = predictor.predict(cascade_path)
    
    print(f"\nAnalyzing cascade path: {cascade_path}")
    print(f"Observed Retweets (within {predictor.config.observation_time/3600:.1f} hours): {observed_count}")
    print(f"Predicted Additional Retweets: {predicted_delta:.2f}")
    print(f"Total Predicted Retweets: {total_prediction:.2f}")
    print(f"Actual Total Retweets: {actual_retweets}")
    print(f"Prediction Error: {abs(total_prediction - actual_retweets):.2f}")

if __name__ == "__main__":
    main() 