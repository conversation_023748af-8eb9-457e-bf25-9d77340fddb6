{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2.7.0+cu126\n"]}], "source": ["import torch\n", "print(torch.__version__)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniforge3/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/plain": ["BertForSequenceClassification(\n", "  (bert): <PERSON><PERSON><PERSON><PERSON>(\n", "    (embeddings): <PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "      (word_embeddings): Embedding(119547, 768, padding_idx=0)\n", "      (position_embeddings): Embedding(512, 768)\n", "      (token_type_embeddings): Embedding(2, 768)\n", "      (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "      (dropout): Dropout(p=0.1, inplace=False)\n", "    )\n", "    (encoder): <PERSON><PERSON><PERSON><PERSON>(\n", "      (layer): ModuleList(\n", "        (0-11): 12 x <PERSON><PERSON><PERSON><PERSON>(\n", "          (attention): <PERSON><PERSON><PERSON><PERSON>(\n", "            (self): BertSdpaSelfAttention(\n", "              (query): Linear(in_features=768, out_features=768, bias=True)\n", "              (key): Linear(in_features=768, out_features=768, bias=True)\n", "              (value): Linear(in_features=768, out_features=768, bias=True)\n", "              (dropout): Dropout(p=0.1, inplace=False)\n", "            )\n", "            (output): BertSelfOutput(\n", "              (dense): Linear(in_features=768, out_features=768, bias=True)\n", "              (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "              (dropout): Dropout(p=0.1, inplace=False)\n", "            )\n", "          )\n", "          (intermediate): BertIntermediate(\n", "            (dense): Linear(in_features=768, out_features=3072, bias=True)\n", "            (intermediate_act_fn): GELUActivation()\n", "          )\n", "          (output): BertOutput(\n", "            (dense): Linear(in_features=3072, out_features=768, bias=True)\n", "            (LayerNorm): LayerNorm((768,), eps=1e-12, elementwise_affine=True)\n", "            (dropout): Dropout(p=0.1, inplace=False)\n", "          )\n", "        )\n", "      )\n", "    )\n", "    (pooler): <PERSON><PERSON><PERSON><PERSON>(\n", "      (dense): Linear(in_features=768, out_features=768, bias=True)\n", "      (activation): <PERSON><PERSON>()\n", "    )\n", "  )\n", "  (dropout): Dropout(p=0.1, inplace=False)\n", "  (classifier): Linear(in_features=768, out_features=28, bias=True)\n", ")"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "\n", "#model_path = \"./bert-multilingual-go-emtions\"\n", "model_path = \"./\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_path)\n", "model = AutoModelForSequenceClassification.from_pretrained(model_path)\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "model.to(device)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Predicted class: gratitude（感激）\n"]}], "source": ["import torch.nn.functional as F\n", "# Prepare input text\n", "text = \"非常有趣的视频。谢谢分享。\"\n", "inputs = tokenizer(text, return_tensors=\"pt\").to(device)\n", "\n", "# Run inference\n", "with torch.no_grad():\n", "    outputs = model(**inputs)\n", "\n", "# Process output for sequence classification\n", "predictions = F.softmax(outputs.logits, dim=-1)\n", "predicted_class_index = torch.argmax(predictions, dim=-1).item()\n", "predicted_class = model.config.id2label[predicted_class_index]\n", "print(f\"Predicted class: {predicted_class}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SequenceClassifierOutput(loss=None, logits=tensor([[ 0.2343, -5.8555, -7.4645, -5.5064, -5.2260, -6.4635, -6.9588, -6.2898,\n", "         -7.0572, -7.5753, -7.1986, -6.9030, -6.1203, -4.3110, -6.6519,  5.4483,\n", "         -7.6105, -4.5950, -6.2156, -7.4344, -5.6264, -4.5750, -5.4078, -5.6764,\n", "         -6.3891, -7.8874, -5.9947, -6.5164]], device='cuda:0'), hidden_states=None, attentions=None)\n"]}], "source": ["print(outputs)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Device set to use cuda:0\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[{'label': 'gratitude（感激）', 'score': 0.9957148432731628}]\n"]}], "source": ["from transformers import pipeline\n", "\n", "text = \"非常有趣的视频。谢谢分享。\"\n", "nlp = pipeline(\"sentiment-analysis\", model = model, tokenizer = tokenizer)\n", "\n", "result = nlp(text)\n", "\n", "print(result)"]}], "metadata": {"kernelspec": {"display_name": "Python (base)", "language": "python", "name": "base"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}