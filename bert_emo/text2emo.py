import torch
import os
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline

# Get the directory where this script is located
current_dir = os.path.dirname(os.path.abspath(__file__))
model_path = current_dir

tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForSequenceClassification.from_pretrained(model_path)

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)

nlp = pipeline("sentiment-analysis", model=model, tokenizer=tokenizer)

def text2emo(text: str):
    """
    Convert input text to its emotion analysis.
    
    Args:
        text (str): Input text to analyze
        
    Returns:
        The emotion analysis result
    """
    return nlp(text)

# Example usage
if __name__ == "__main__":
    text = "非常有趣的视频。谢谢分享。"
    result = text2emo(text)
    print(result)